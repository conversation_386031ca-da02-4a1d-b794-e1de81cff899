from openai import OpenAI
from dotenv import load_dotenv
import os
import base64
from typing import Union, Optional

load_dotenv()
API_KEY = os.getenv("OPEN_AI_KEY")
client = OpenAI(api_key=API_KEY)


def generate_text_content(prompt: str) -> str:
    """
    Generate 120-word quality content based on text prompt.

    Args:
        prompt (str): Brief description or keywords for content generation

    Returns:
        str: 120-word quality content
    """
    system_prompt = """You are a professional content writer specializing in creating engaging, informative, and high-quality content. 
    
    Your task is to:
    1. Take a brief prompt (words or 1-2 sentences) and expand it into exactly 120 words
    2. Create content that is engaging, well-structured, and valuable to readers
    3. Use clear, professional language with proper grammar and flow
    4. Include relevant details and context to make the content comprehensive
    5. Ensure the content is original and creative while staying true to the prompt
    
    Guidelines:
    - Write in a professional yet accessible tone
    - Structure content with logical flow
    - Include specific details and examples when appropriate
    - Make content informative and engaging
    - Ensure exactly 120 words (no more, no less)
    """

    user_prompt = f"""Based on the following brief description, create exactly 120 words of high-quality, engaging content:

"{prompt}"

Please ensure the content is:
- Exactly 120 words
- Professional and well-written
- Engaging and informative
- include hashtags and emojis
- Relevant to the given prompt
- Well-structured with good flow"""

    try:
        response = client.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            max_tokens=200,
            temperature=0.7
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        return f"Error generating content: {str(e)}"


def generate_image_content(image_path: str, additional_context: Optional[str] = None) -> str:
    """
    Generate 120-word social media content based on image analysis.

    Args:
        image_path (str): Path to the image file
        additional_context (str, optional): Additional context or requirements

    Returns:
        str: 120-word social media content based on image
    """
    system_prompt = """You are a professional social media content creator and strategist. Your task is to analyze images and create engaging social media content.
    
    When analyzing images, you should:
    1. Identify the visual elements, mood, and potential audience appeal
    2. Create compelling social media content that would engage followers
    3. Write exactly 120 words of high-quality social media content
    4. Make the content shareable, relatable, and engaging
    5. Include relevant hashtags or social media elements when appropriate
    
    Guidelines for social media content:
    - Create content that encourages engagement (likes, comments, shares)
    - Use conversational and relatable language
    - Make it relevant to current social media trends
    - Include call-to-actions when appropriate
    - Ensure exactly 120 words
    - Make content that fits various social media platforms (Instagram, Facebook, Twitter, LinkedIn)
    - Focus on storytelling and emotional connection"""

    try:
        # Encode image to base64
        with open(image_path, "rb") as image_file:
            image_data = base64.b64encode(image_file.read()).decode('utf-8')

        user_prompt = f"""Analyze this image and create exactly 120 words of engaging social media content that would perform well on platforms like Instagram, Facebook, Twitter, or LinkedIn.
        
        {f'Additional context: {additional_context}' if additional_context else ''}
        
        Please create social media content that:
        - Is engaging and shareable
        - Encourages audience interaction
        - Exactly 120 words
        - Professional yet relatable
        - Captures the essence and appeal of the image
        - Would work well across different social media platforms"""

        response = client.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": system_prompt},
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": user_prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_data}"
                            }
                        }
                    ]
                }
            ],
            max_tokens=200,
            temperature=0.7
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        return f"Error generating social media content: {str(e)}"


def generate_video_content(video_path: str, additional_context: Optional[str] = None) -> str:
    """
    Generate 120-word social media content based on video analysis.

    Args:
        video_path (str): Path to the video file
        additional_context (str, optional): Additional context or requirements

    Returns:
        str: 120-word social media content based on video
    """
    system_prompt = """You are a professional social media content creator and strategist. Your task is to analyze videos and create engaging social media content.
    
    When analyzing videos, you should:
    1. Identify the visual elements, motion, mood, and potential audience appeal
    2. Consider the video's narrative, pacing, and emotional impact
    3. Create compelling social media content that would engage followers
    4. Write exactly 120 words of high-quality social media content
    5. Make the content shareable, relatable, and engaging
    6. Include relevant hashtags or social media elements when appropriate
    
    Guidelines for social media content:
    - Create content that encourages engagement (likes, comments, shares)
    - Use conversational and relatable language
    - Make it relevant to current social media trends
    - Include call-to-actions when appropriate
    - Ensure exactly 120 words
    - Make content that fits various social media platforms (Instagram, Facebook, Twitter, LinkedIn, TikTok, YouTube)
    - Focus on storytelling and emotional connection
    - Consider video-specific elements like motion, sound, and narrative flow"""

    try:
        # Encode video to base64
        with open(video_path, "rb") as video_file:
            video_data = base64.b64encode(video_file.read()).decode('utf-8')

        user_prompt = f"""Analyze this video and create exactly 120 words of engaging social media content that would perform well on platforms like Instagram, Facebook, Twitter, LinkedIn, TikTok, or YouTube.
        
        {f'Additional context: {additional_context}' if additional_context else ''}
        
        Please create social media content that:
        - Is engaging and shareable
        - Encourages audience interaction
        - Exactly 120 words
        - Professional yet relatable
        - Captures the essence and appeal of the video
        - Would work well across different social media platforms
        - Considers the video's motion, narrative, and emotional impact"""

        response = client.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": system_prompt},
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": user_prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:video/mp4;base64,{video_data}"
                            }
                        }
                    ]
                }
            ],
            max_tokens=200,
            temperature=0.7
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        return f"Error generating social media content: {str(e)}"


def generate_media_content(media_path: str, media_type: str = "auto", additional_context: Optional[str] = None) -> str:
    """
    Generate 120-word social media content based on media analysis (image or video).

    Args:
        media_path (str): Path to the media file (image or video)
        media_type (str): Type of media - "image", "video", or "auto" (auto-detect)
        additional_context (str, optional): Additional context or requirements

    Returns:
        str: 120-word social media content based on media
    """
    # Auto-detect media type based on file extension
    if media_type == "auto":
        file_extension = os.path.splitext(media_path)[1].lower()
        if file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
            media_type = "image"
        elif file_extension in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv']:
            media_type = "video"
        else:
            return f"Error: Unsupported file type {file_extension}. Supported formats: images (.jpg, .jpeg, .png, .gif, .bmp, .webp) and videos (.mp4, .avi, .mov, .wmv, .flv, .webm, .mkv)"
    
    if media_type == "image":
        return generate_image_content(media_path, additional_context)
    elif media_type == "video":
        return generate_video_content(media_path, additional_context)
    else:
        return f"Error: Invalid media type '{media_type}'. Use 'image', 'video', or 'auto'."


def generate_response_ai(prompt: Union[str, dict]) -> str:
    """
    Main function to handle text, image, and video content generation.

    Args:
        prompt: Either a string (for text content) or dict with:
            - 'media_path' (required): Path to image or video file
            - 'media_type' (optional): "image", "video", or "auto" (default: "auto")
            - 'context' (optional): Additional context or requirements

    Returns:
        str: Generated content
    """
    if isinstance(prompt, str):
        return generate_text_content(prompt)
    elif isinstance(prompt, dict):
        media_path = prompt.get('media_path')
        media_type = prompt.get('media_type', 'auto')
        context = prompt.get('context')
        
        if media_path:
            return generate_media_content(media_path, media_type, context)
        else:
            return "Error: 'media_path' is required for media content generation"
    else:
        return "Error: Invalid prompt format. Use string for text or dict for media content generation."
